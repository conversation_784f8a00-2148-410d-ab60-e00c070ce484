import type { Options } from '@wdio/types';
import { join } from 'path';
import { config as sauceSharedConfig } from './wdio.saucelabs.shared.conf.ts';
import 'expect-webdriverio';

const build = 'WebdriverIO-PWA';

export const config: Options.Testrunner = {
  ...sauceSharedConfig,
  automationProtocol: 'webdriver',
  capabilities: [
    {
      // iPhone 15 Pro Max - Latest real iOS device

      'appium:deviceName': 'iPhone 14.*',
      'appium:automationName': 'XCUITest',
      'appium:platformVersion': '18',
      'appium:autoAcceptAlerts': false,
      browserName: 'Safari',
      platformName: 'iOS',
      //maxInstances: 1,
      'sauce:options': {
        appiumVersion: 'latest', // Latest stable Appium version
        build,
      },
      'appium:safariAllowPopups': true,
      webSocketUrl: false,
    },
    /*{
      // iPhone 15 Pro Max - Latest real iOS device
      'appium:deviceName': 'iPhone.*',
      'appium:automationName': 'XCUITest',
      'appium:platformVersion': '18.*',
      'appium:autoAcceptAlerts': false,
      browserName: 'Safari',
      platformName: 'iOS',
      'sauce:options': {
        appiumVersion: 'stable', // Latest stable Appium version
        build,
      },
      webSocketUrl: false,
    },*/
    //{
    // iPhone 15 Pro Max - Latest real iOS device
    //'appium:deviceName': 'iPhone 13.*',
    //'appium:automationName': 'XCUITest',
    //'appium:platformVersion': '18.*',
    //'appium:autoAcceptAlerts': false,
    //browserName: 'Safari',
    //platformName: 'iOS',
    //'sauce:options': {
    //appiumVersion: 'latest', // Latest stable Appium version
    //build,
    //},
    //webSocketUrl: false,
    //},
    // {
    //   // iPhone 15 - Another latest real iOS device
    //   'appium:deviceName': 'iPhone 15',
    //   'appium:automationName': 'XCUITest',
    //   browserName: 'Safari',
    //   platformName: 'iOS',
    //   'sauce:options': {
    //     appiumVersion: 'latest',
    //     build,
    //   },
    // },
    //  {
    //   // Samsung Galaxy S24 Ultra - Latest real Android device

    //   'appium:deviceName': 'Samsung.*',
    //   'appium:automationName': 'UiAutomator2',
    //   'appium:platformVersion': '14',
    //   browserName: 'Chrome',
    //   platformName: 'Android',
    //   'sauce:options': {
    //     appiumVersion: 'latest',
    //     build,
    //     extendedDebugging: false,
    //     capturePerformance: false,
    //   },
    //    // Chrome-specific capabilities at the root level
    //    'goog:chromeOptions': {
    //     args: [
    //         '--start-maximized',
    //         '--disable-notifications',
    //         '--disable-popup-blocking'
    //     ]
    // },
    //   webSocketUrl: false
    // },
    /*{
      //Google Pixel 8 Pro - Latest real Android device
      "appium:deviceName": "Google Pixel 8.*",
      'appium:platformVersion': '15',
      "appium:automationName": "UiAutomator2",
      browserName: "Chrome",
      platformName: "Android",
      "sauce:options": {
        appiumVersion: "stable",
        build,
      },
      webSocketUrl: false,
      unhandledPromptBehavior:'dismiss'
    },*/
  ],

  // Add these important configurations
  services: [
    [
      'sauce',
      {
        region: 'us-west-1',
      },
    ],
  ],
  maxInstances: 5,
  waitforTimeout: 30000,
  connectionRetryTimeout: 240000,
  connectionRetryCount: 3,
  framework: 'cucumber',
  specFileRetries: 0,
  specFileRetriesDelay: 0,
  specFileRetriesDeferred: false,

  // Cucumber specific configuration
  cucumberOpts: {
    require: [join(process.cwd(), './tests/step-definitions/*.ts')],
    backtrace: true,
    requireModule: [],
    dryRun: false,
    failFast: false,
    snippets: true,
    source: true,
    strict: true,
    //tags: "@GrizzlyMOMapviewaddingShortcut_QAstage or @GrizzlyMOShowdirectionsaddingShortcut_QAstage or @GrizzlyMOStoreListMapitaddingShortcut_QAstage or @GrizzlyMOStoreListaddingShortcut_QAstage or @GrizzlyMOStoredetailsaddingShortcut_QAstage or @GrizzlyMOCouponHomePageShortcut_QAstage",
    tags: '@CamelValidateAlthome_QA',
    timeout: 2400000,
    ignoreUndefinedDefinitions: false,
    scenarioLevelReporter: true,
    logLevel: 'verbose',
    failAmbiguousDefinitions: true,
    //retry: 0,
    retryTagFilter: '', // Only retry scenarios tagged with @flaky

  },
};
