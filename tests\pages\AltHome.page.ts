import Page from './Page.ts';
import elementActions from '../support/actions/element.actions.ts';
import couponHomePageObject from '../pageObjects/CouponHome.pageObject.ts';
import LocationServicesPageObjects from '../pageObjects/LocationServices.pageObjects.ts';
import AltHomePageObjects from '../pageObjects/AltHome.pageObjects.ts';

class AltHome extends Page {

    public async validateUserOnActiveCouponPage() {
        couponHomePageObject.allCoupons.forEach(async coupon => {
            await expect(await coupon).toBeDisplayed();
        });
    }

    async validateUserIsOnAltHomePage() {
        await expect(await AltHomePageObjects.redeemNowBtn).toBeDisplayed();
        await expect(await AltHomePageObjects.navBarLogo).toBeDisplayed();
        await expect(await AltHomePageObjects.couponCount).toBeDisplayed();
        await expect(await AltHomePageObjects.welcomeBackMsg).toBeDisplayed();
    }

    async validateAltHomePage(brand: string): Promise<void> {

        if (driver.isIOS) {

            await expect(await AltHomePageObjects.redeemNowBtn).toBeDisplayed();
            await expect(await AltHomePageObjects.navBarLogo).toBeDisplayed();
            await expect(await AltHomePageObjects.couponCount).toBeDisplayed();
            await expect(await AltHomePageObjects.welcomeBackMsg).toBeDisplayed();
            if (brand == 'GRIZZLYMO') {
                await expect(await AltHomePageObjects.warningText).toBeDisplayed();
                await expect(await AltHomePageObjects.underageSaleText).toBeDisplayed();
                await expect(await AltHomePageObjects.nicotineProductsText).toBeDisplayed();
            }
            else if (brand == 'CAMEL') {

                await expect(AltHomePageObjects.sgwcamel).toBeDisplayed();
            }
            await expect(AltHomePageObjects.tile1Image).toBeDisplayed();
            await expect(AltHomePageObjects.tile2Image).toBeDisplayed();
            await expect(AltHomePageObjects.tile3Image).toBeDisplayed();
            
            await elementActions.click(AltHomePageObjects.tile1Image);
            console.log('Clicked on Tile 1 Image');
            await driver.acceptAlert();
            await this.handleMobileNotificationPermission();
            const windows = await driver.getWindowHandles();
            console.log(windows);
            await driver.switchToWindow(windows[1]);

            await driver.switchContext('NATIVE_APP');
            if (driver.isIOS) {
                await elementActions.clickElementIfExistsAndVisible(await LocationServicesPageObjects.iOSLocationServiceAllowBtn);
                const contexts = await driver.getContexts();
                const webviewContext = contexts.find(context => context.toString().includes('WEBVIEW'));
            }

            //const windows = await driver.getWindowHandles();
            //await driver.switchToWindow(windows[1]);
            //await driver.closeWindow();

            //await elementActions.clickElementIfExistsAndVisible(AltHomePageObjects.tile2Image);


        }
    }
}




export default new AltHome();

