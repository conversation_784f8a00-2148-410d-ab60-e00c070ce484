import Page from './Page.ts';
import elementActions from '../support/actions/element.actions.ts';
import couponHomePageObject from '../pageObjects/CouponHome.pageObject.ts';
import LocationServicesPageObjects from '../pageObjects/LocationServices.pageObjects.ts';
import AltHomePageObjects from '../pageObjects/AltHome.pageObjects.ts';

class AltHome extends Page {

    public async validateUserOnActiveCouponPage() {
        couponHomePageObject.allCoupons.forEach(async coupon => {
            await expect(await coupon).toBeDisplayed();
        });
    }

    /**
     * Clicks tile1 and handles popup immediately to prevent hanging
     * This method runs popup handling in parallel with the click to avoid timeouts
     */
    private async clickTile1WithPopupHandling(): Promise<void> {
        try {
            console.log('Starting tile1 click with popup handling...');

            // Start popup handling in the background immediately
           // this.handlePopupInBackground();

            // Perform the click
            await this.performTile1Click();

            // Wait a moment for popup handling to complete
            await driver.pause(2000);

            console.log('Tile1 click and popup handling completed');

        } catch (error) {
            console.log('Error in tile1 click with popup handling:', error);
            throw error;
        }
    }

    /**
     * Performs the actual click on tile1 using JavaScript click with timeout
     */
    private async performTile1Click(): Promise<void> {
        try {
            console.log('Performing tile1 click using JavaScript...');

            // Use JavaScript click with a timeout to prevent hanging
            const tile1Element = await AltHomePageObjects.tile1Image;

            // Use Promise.race to timeout the JavaScript click if it hangs
            await Promise.race([
                driver.execute('arguments[0].click();', tile1Element),
                new Promise((_, reject) =>
                    setTimeout(() => reject(new Error('JavaScript click timeout')), 5000),
                ),
            ]);

            console.log('Tile1 JavaScript click completed');
        } catch (error) {
            console.log('JavaScript click failed or timed out:', error);
        }
    }

    /**
     * Handles popup in background immediately after click starts
     */
    private async handlePopupInBackground(): Promise<void> {
        try {
            console.log('Starting background popup handling...');

            // Wait a short moment for popup to appear
            await driver.pause(1000);

            // First try to handle Safari popup permission dialog (for iOS)
            if (driver.isIOS) {
                // Add timeout to prevent hanging on getContext
                await Promise.race([
                    this.handleSafariPopupPermission(),
                    new Promise((_, reject) =>
                        setTimeout(() => reject(new Error('Safari popup handling timeout')), 10000),
                    ),
                ]);
            }

            // Then try to handle JavaScript alerts with timeout
            await Promise.race([
                this.handleJavaScriptAlert(),
                new Promise((_, reject) =>
                    setTimeout(() => reject(new Error('JavaScript alert handling timeout')), 5000),
                ),
            ]);

        } catch (error) {
            console.log('Error in background popup handling:', error);
        }
    }

    /**
     * Handles Safari popup permission dialog that appears when clicking tile1
     */
    private async handleSafariPopupPermission(): Promise<void> {
        try {
            console.log('Checking for Safari popup permission dialog...');

            // Get current context to switch back later with timeout
            const currentContext = await Promise.race([
                driver.getContext(),
                new Promise((_, reject) =>
                    setTimeout(() => reject(new Error('getContext timeout')), 3000),
                ),
            ]) as string;

            // Switch to native context to handle Safari popup with timeout
            await Promise.race([
                driver.switchContext('NATIVE_APP'),
                new Promise((_, reject) =>
                    setTimeout(() => reject(new Error('switchContext timeout')), 3000),
                ),
            ]);

            // Look for "Allow" button in Safari popup permission dialog
            const allowButton = $('//XCUIElementTypeButton[@name="Allow"]');

            // Wait for the Allow button to appear
            await allowButton.waitForDisplayed({
                timeout: 10000,
                timeoutMsg: 'Safari Allow button not found',
            });

            if (await allowButton.isDisplayed()) {
                await allowButton.click();
                console.log('Safari popup Allow button clicked successfully');
                await driver.pause(1000);
            }

            // Switch back to webview context
            const contexts = await driver.getContexts();
            const webviewContext = contexts.find(context => context.toString().includes('WEBVIEW'));
            if (webviewContext) {
                await driver.switchContext(webviewContext.toString());
                console.log('Switched back to webview context');
            } else if (currentContext) {
                await driver.switchContext(currentContext);
                console.log('Switched back to original context');
            }

        } catch (error) {
            console.log('Safari popup permission dialog not found or already handled:', error);

            // Ensure we're back in webview context even if there was an error
            try {
                const contexts = await driver.getContexts();
                const webviewContext = contexts.find(context => context.toString().includes('WEBVIEW'));
                if (webviewContext) {
                    await driver.switchContext(webviewContext.toString());
                }
            } catch (contextError) {
                console.log('Error switching back to webview context:', contextError);
            }
        }
    }

    /**
     * Handles JavaScript alerts
     */
    private async handleJavaScriptAlert(): Promise<void> {
        try {
            console.log('Checking for JavaScript alerts...');

            // Wait for alert to appear with a reasonable timeout
            await driver.waitUntil(
                async () => {
                    try {
                        await driver.getAlertText();
                        return true;
                    } catch (error) {
                        return false;
                    }
                },
                {
                    timeout: 10000,
                    interval: 500,
                    timeoutMsg: 'JavaScript alert did not appear within 10 seconds',
                },
            );

            // Get alert text for logging
            const alertText = await driver.getAlertText();
            console.log('JavaScript alert text:', alertText);

            // Accept the alert
            await driver.acceptAlert();
            console.log('JavaScript alert accepted successfully');

        } catch (error) {
            console.log('No JavaScript alert found:', error);
        }
    }

    async validateUserIsOnAltHomePage() {
        await expect(await AltHomePageObjects.redeemNowBtn).toBeDisplayed();
        await expect(await AltHomePageObjects.navBarLogo).toBeDisplayed();
        await expect(await AltHomePageObjects.couponCount).toBeDisplayed();
        await expect(await AltHomePageObjects.welcomeBackMsg).toBeDisplayed();
    }

    async validateAltHomePage(brand: string): Promise<void> {

        if (driver.isIOS) {

            await expect(await AltHomePageObjects.redeemNowBtn).toBeDisplayed();
            await expect(await AltHomePageObjects.navBarLogo).toBeDisplayed();
            await expect(await AltHomePageObjects.couponCount).toBeDisplayed();
            await expect(await AltHomePageObjects.welcomeBackMsg).toBeDisplayed();
            if (brand == 'GRIZZLYMO') {
                await expect(await AltHomePageObjects.warningText).toBeDisplayed();
                await expect(await AltHomePageObjects.underageSaleText).toBeDisplayed();
                await expect(await AltHomePageObjects.nicotineProductsText).toBeDisplayed();
            }
            else if (brand == 'CAMEL') {

                await expect(AltHomePageObjects.sgwcamel).toBeDisplayed();
            }
            await expect(AltHomePageObjects.tile1Image).toBeDisplayed();
            await expect(AltHomePageObjects.tile2Image).toBeDisplayed();
            await expect(AltHomePageObjects.tile3Image).toBeDisplayed();
            
            // Use a more robust click approach for tile1 that handles popups immediately
            await this.clickTile1WithPopupHandling();
            console.log('Clicked on Tile 1 Image and handled popup');

            // Handle mobile notification permission
            await this.handleMobileNotificationPermission();

            // Get window handles and switch to new window if available
            const windows = await driver.getWindowHandles();
            console.log('Available windows:', windows);
            if (windows.length > 1) {
                await driver.switchToWindow(windows[1]);
                console.log('Switched to new window');
            }

            await driver.switchContext('NATIVE_APP');
            if (driver.isIOS) {
                await elementActions.clickElementIfExistsAndVisible(LocationServicesPageObjects.iOSLocationServiceAllowBtn);
                const contexts = await driver.getContexts();
                const webviewContext = contexts.find(context => context.toString().includes('WEBVIEW'));

                // Switch back to webview context if available
                if (webviewContext) {
                    await driver.switchContext(webviewContext.toString());
                    console.log('Switched back to webview context');
                }
            }

            //const windows = await driver.getWindowHandles();
            //await driver.switchToWindow(windows[1]);
            //await driver.closeWindow();

            //await elementActions.clickElementIfExistsAndVisible(AltHomePageObjects.tile2Image);


        }
    }
}




export default new AltHome();

